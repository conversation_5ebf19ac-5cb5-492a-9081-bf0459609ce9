name: Test

on: [push, pull_request]

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: pip
        cache-dependency-path: pyproject.toml
    - name: Install dependencies
      run: |
        pip install -e '.[all]'
    - name: Run tests
      run: |
        pytest
    - name: Run tests with coverage
      run: |
        pytest --cov=llm_grok --cov-report=xml --cov-report=term-missing
    - name: Type checking
      run: |
        mypy llm_grok --strict
    - name: Linting
      run: |
        ruff check llm_grok/